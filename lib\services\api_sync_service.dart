import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:connectivity_plus/connectivity_plus.dart';
import '../config/api_config.dart';
import '../services/auth_service.dart';
import '../services/database_service.dart';
import '../models/hospital.dart';
import '../models/patient.dart';

class ApiSyncService {
  static final ApiSyncService _instance = ApiSyncService._internal();
  final _authService = AuthService();
  final _databaseService = DatabaseService();
  final _connectivity = Connectivity();

  factory ApiSyncService() => _instance;
  ApiSyncService._internal();

  /// Vérifie la connectivité Internet
  Future<bool> hasInternetConnection() async {
    try {
      final connectivityResult = await _connectivity.checkConnectivity();
      return connectivityResult != ConnectivityResult.none;
    } catch (e) {
      print('Erreur lors de la vérification de la connectivité: $e');
      return false;
    }
  }

  /// Synchronisation complète des données
  Future<void> performFullSync() async {
    if (!await hasInternetConnection()) {
      throw Exception('Pas de connexion Internet disponible');
    }

    try {
      print('Début de la synchronisation complète...');
      
      // 1. Synchroniser les données initiales (publiques)
      await _syncInitialData();
      
      // 2. Synchroniser les données utilisateur (authentifiées)
      await _syncUserData();
      
      print('Synchronisation complète terminée avec succès');
    } catch (e) {
      print('Erreur lors de la synchronisation: $e');
      rethrow;
    }
  }

  /// Synchronise les données initiales (hôpitaux, services, spécialités)
  Future<void> _syncInitialData() async {
    try {
      final response = await http.get(
        Uri.parse('${ApiConfig.baseUrl}${ApiConfig.initialDataEndpoint}'),
        headers: {'Content-Type': 'application/json'},
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        if (data['status'] == 'success') {
          await _storeInitialData(data['data']);
        } else {
          throw Exception('Erreur API: ${data['message']}');
        }
      } else {
        throw Exception('Erreur HTTP: ${response.statusCode}');
      }
    } catch (e) {
      print('Erreur lors de la synchronisation des données initiales: $e');
      rethrow;
    }
  }

  /// Synchronise les données spécifiques à l'utilisateur
  Future<void> _syncUserData() async {
    try {
      final headers = _authService.getAuthHeaders();
      final response = await http.get(
        Uri.parse('${ApiConfig.baseUrl}${ApiConfig.userDataEndpoint}'),
        headers: headers,
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        if (data['status'] == 'success') {
          await _storeUserData(data['data']);
        } else {
          throw Exception('Erreur API: ${data['message']}');
        }
      } else if (response.statusCode == 401) {
        // Token expiré, tenter de le rafraîchir
        final refreshed = await _authService.refreshToken();
        if (refreshed) {
          return await _syncUserData(); // Réessayer
        } else {
          throw Exception('Session expirée, veuillez vous reconnecter');
        }
      } else {
        throw Exception('Erreur HTTP: ${response.statusCode}');
      }
    } catch (e) {
      print('Erreur lors de la synchronisation des données utilisateur: $e');
      rethrow;
    }
  }

  /// Stocke les données initiales en base locale
  Future<void> _storeInitialData(Map<String, dynamic> data) async {
    try {
      final db = await _databaseService.database;
      
      // Stocker les hôpitaux
      if (data['hospitals'] != null) {
        await db.delete('hospital'); // Nettoyer les anciennes données
        for (final hospitalData in data['hospitals']) {
          final hospital = Hospital.fromJson(hospitalData);
          await db.insert('hospital', hospital.toMap());
        }
        print('${data['hospitals'].length} hôpitaux synchronisés');
      }

      // Stocker les services et spécialités si nécessaire
      // TODO: Ajouter les tables services et specialities si requis
      
    } catch (e) {
      print('Erreur lors du stockage des données initiales: $e');
      rethrow;
    }
  }

  /// Stocke les données utilisateur en base locale
  Future<void> _storeUserData(Map<String, dynamic> data) async {
    try {
      final db = await _databaseService.database;
      
      // Stocker les patients
      if (data['role_data']?['patients'] != null) {
        final patients = data['role_data']['patients'] as List;
        for (final patientData in patients) {
          // Adapter les données API au modèle local Patient
          final adaptedPatient = _adaptApiPatientToLocal(patientData);
          await db.insert('patient', adaptedPatient, 
            conflictAlgorithm: ConflictAlgorithm.replace);
        }
        print('${patients.length} patients synchronisés');
      }

      // Stocker les rendez-vous
      if (data['role_data']?['appointments'] != null) {
        // TODO: Implémenter le stockage des rendez-vous
        print('Rendez-vous à synchroniser: ${data['role_data']['appointments'].length}');
      }
      
    } catch (e) {
      print('Erreur lors du stockage des données utilisateur: $e');
      rethrow;
    }
  }

  /// Adapte les données patient de l'API au format local
  Map<String, dynamic> _adaptApiPatientToLocal(Map<String, dynamic> apiPatient) {
    // Séparer le nom complet en prénom et nom
    final fullName = apiPatient['name'] as String? ?? '';
    final nameParts = fullName.split(' ');
    final firstname = nameParts.isNotEmpty ? nameParts.first : '';
    final lastname = nameParts.length > 1 ? nameParts.sublist(1).join(' ') : '';

    return {
      'server_id': apiPatient['id'],
      'firstname': firstname,
      'lastname': lastname,
      'sexe': 'F', // Par défaut pour les patientes enceintes
      'birth_date': apiPatient['birth_date'] ?? '',
      'birth_place': '', // Non fourni par l'API
      'tel': apiPatient['tel'] ?? '',
      'address': apiPatient['address'] ?? '',
      'husband_name': apiPatient['husband_name'] ?? '',
      'husband_tel': apiPatient['husband_tel'] ?? '',
      'assurance': apiPatient['assurance'] ?? '',
      'occupation': '', // Non fourni par l'API
      'study_level': '', // Non fourni par l'API
      'language': '', // Non fourni par l'API
      'first_cpn_date': apiPatient['active_pregnancy']?['start_date'] ?? '',
      'pregnancy_term': _parsePregnancyTerm(apiPatient['active_pregnancy']?['term']),
      'expected_delivery_date': '', // À calculer
      'next_appointment_date': '', // À récupérer des rendez-vous
      'pregnancy_end_date': '', // Non fourni
      'sms_consent': 1, // Par défaut
      'agent_id': _authService.currentUser?.id ?? 0,
      'agent_server_id': _authService.currentUser?.id ?? 0,
      'created_at': DateTime.now().toIso8601String(),
      'updated_at': DateTime.now().toIso8601String(),
      'sync_status': 1, // Marqué comme synchronisé
      'last_sync_date': DateTime.now().toIso8601String(),
    };
  }

  /// Parse le terme de grossesse depuis le format API ("28 SA" -> 28)
  int _parsePregnancyTerm(String? term) {
    if (term == null) return 0;
    final match = RegExp(r'(\d+)').firstMatch(term);
    return match != null ? int.parse(match.group(1)!) : 0;
  }

  /// Synchronisation périodique (à appeler régulièrement)
  Future<void> periodicSync() async {
    try {
      if (await hasInternetConnection()) {
        await performFullSync();
      }
    } catch (e) {
      print('Erreur lors de la synchronisation périodique: $e');
      // Ne pas propager l'erreur pour éviter de bloquer l'app
    }
  }
}
