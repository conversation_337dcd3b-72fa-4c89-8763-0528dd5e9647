# 📱 API Mobile PTCCare - Documentation Développeur Flutter

## 🎯 Vue d'ensemble

Cette API REST sécurisée avec JWT est conçue spécifiquement pour l'application mobile Flutter PTCCare. Elle permet l'authentification des agents de santé (administrateurs, médecins, assistants) et la synchronisation des données pour le fonctionnement hors ligne.

## 🔐 Authentification JWT

### Base URL
```
http://localhost:8000/api/mobile/
```

### 1. Connexion
**POST** `/auth/login`

**Body:**
```json
{
  "email": "dr.kou<PERSON><EMAIL>",
  "password": "doctor123"
}
```

**Réponse succès (200):**
```json
{
  "status": "success",
  "message": "Connexion réussie",
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "token_type": "Bearer",
  "expires_in": 604800,
  "user": {
    "id": 2,
    "username": "DOC-12345678",
    "email": "dr.k<PERSON><PERSON><PERSON>@ptccare.com",
    "profile_id": 2,
    "role": "docteur",
    "name": "Jean KOUASSI",
    "firstname": "Jean",
    "lastname": "KOUASSI",
    "tel": "+229 97 11 11 11",
    "hospital_id": 1,
    "hospital_name": "Centre Hospitalier Départemental (CHD)",
    "service_id": 1,
    "service_name": "Gynécologie-Obstétrique",
    "speciality_id": 1,
    "speciality_name": "Gynécologue-Obstétricien",
    "must_change_password": false
  }
}
```

**Codes d'erreur:**
- `400` : Données manquantes (`MISSING_CREDENTIALS`, `INVALID_JSON`)
- `401` : Identifiants invalides (`INVALID_CREDENTIALS`)
- `403` : Rôle non autorisé (`ROLE_NOT_ALLOWED`)
- `404` : Profil non trouvé (`PROFILE_NOT_FOUND`)
- `500` : Erreur serveur (`SERVER_ERROR`)

### 2. Rafraîchir le token
**POST** `/auth/refresh`

**Headers:**
```
Authorization: Bearer <token>
```

**Réponse succès (200):**
```json
{
  "status": "success",
  "message": "Token rafraîchi",
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "token_type": "Bearer",
  "expires_in": 604800
}
```

### 3. Vérifier le token
**GET** `/auth/verify`

**Headers:**
```
Authorization: Bearer <token>
```

**Réponse succès (200):**
```json
{
  "status": "success",
  "message": "Token valide",
  "user_id": 2,
  "role": "docteur",
  "expires_at": 1752099272
}
```

### 4. Déconnexion
**POST** `/auth/logout`

**Headers:**
```
Authorization: Bearer <token>
```

**Réponse succès (200):**
```json
{
  "status": "success",
  "message": "Déconnexion réussie"
}
```

## 📊 Endpoints de données

### 1. Données initiales (publiques)
**GET** `/initial-data`

Récupère les données de référence nécessaires au fonctionnement de l'app.

**Réponse succès (200):**
```json
{
  "status": "success",
  "data": {
    "hospitals": [
      {
        "id": 1,
        "name": "Centre Hospitalier Départemental (CHD)",
        "address": "Avenue Jean-Paul II, Cotonou, Bénin",
        "tel": "+229 21 30 01 00"
      }
    ],
    "services": [
      {
        "id": 1,
        "name": "Gynécologie-Obstétrique",
        "description": "Service de Gynécologie-Obstétrique",
        "hospital_id": 1,
        "hospital__name": "Centre Hospitalier Départemental (CHD)"
      }
    ],
    "specialities": [
      {
        "id": 1,
        "name": "Gynécologue-Obstétricien",
        "description": "Spécialité en Gynécologue-Obstétricien"
      }
    ],
    "languages": [
      {
        "id": 1,
        "name": "Français",
        "path": "fr"
      }
    ],
    "app_version": "1.0.0",
    "api_version": "1.0.0",
    "server_time": "2025-01-02T10:30:00Z"
  }
}
```

### 2. Données utilisateur (authentifiées)
**GET** `/user-data`

**Headers:**
```
Authorization: Bearer <token>
```

Récupère les données spécifiques à l'utilisateur connecté selon son rôle.

**Réponse pour un médecin (200):**
```json
{
  "status": "success",
  "data": {
    "user": {
      "id": 2,
      "username": "DOC-12345678",
      "email": "<EMAIL>",
      "profile": {
        "id": 2,
        "firstname": "Jean",
        "lastname": "KOUASSI",
        "tel": "+229 97 11 11 11",
        "sexe": "M",
        "birth_date": "1975-05-15",
        "address": "Quartier Fidjrossè, Cotonou",
        "occupation": "Gynécologue-Obstétricien",
        "hospital_id": 1,
        "service_id": 1,
        "speciality_id": 1,
        "language_id": 1
      },
      "role": "docteur",
      "permissions": [
        "create_patient",
        "edit_patient",
        "create_appointment",
        "edit_appointment",
        "create_pregnancy",
        "edit_pregnancy",
        "view_own_patients",
        "manage_consultations"
      ]
    },
    "role_data": {
      "patients": [
        {
          "id": 4,
          "user_id": 4,
          "username": "PAT-20240001",
          "name": "Akoua MENSAH",
          "tel": "+229 97 44 44 44",
          "birth_date": "1995-06-15",
          "address": "Quartier Gbégamey, Cotonou",
          "assurance": "CNSS",
          "husband_name": "Koffi MENSAH",
          "husband_tel": "+229 97 55 55 55",
          "active_pregnancy": {
            "id": 1,
            "term": "28 SA",
            "state": "En cours",
            "start_date": "2024-06-01"
          }
        }
      ],
      "appointments": [
        {
          "id": 1,
          "patient_id": 4,
          "patient_name": "Akoua MENSAH",
          "consul_date": "2025-01-09",
          "consul_hour": "09:00",
          "appointment_type": "Consultation prénatale",
          "state": "EN ATTENTE",
          "consul_data": "Contrôle de routine, échographie"
        }
      ],
      "total_patients": 3,
      "total_appointments": 3
    }
  }
}
```

## 🔒 Sécurité et Gestion des erreurs

### Headers requis pour les endpoints authentifiés
```
Authorization: Bearer <jwt_token>
Content-Type: application/json
```

### Codes d'erreur standardisés
- `MISSING_TOKEN` : Token d'authentification manquant
- `INVALID_TOKEN` : Token invalide ou expiré
- `AUTH_REQUIRED` : Authentification requise
- `ADMIN_REQUIRED` : Accès administrateur requis
- `DOCTOR_OR_ADMIN_REQUIRED` : Accès médecin ou admin requis
- `USER_NOT_FOUND` : Utilisateur non trouvé
- `SERVER_ERROR` : Erreur serveur interne

### Gestion de l'expiration des tokens
- **Durée de vie** : 7 jours (604800 secondes)
- **Rafraîchissement** : Utiliser `/auth/refresh` avant expiration
- **Stockage sécurisé** : Utiliser le stockage sécurisé Flutter (flutter_secure_storage)

## 📱 Intégration Flutter

### 1. Configuration HTTP Client
```dart
class ApiClient {
  static const String baseUrl = 'http://localhost:8000/api/mobile';
  static String? _token;
  
  static Map<String, String> get headers => {
    'Content-Type': 'application/json',
    if (_token != null) 'Authorization': 'Bearer $_token',
  };
}
```

### 2. Gestion de l'authentification
```dart
class AuthService {
  Future<LoginResponse> login(String email, String password) async {
    final response = await http.post(
      Uri.parse('$baseUrl/auth/login'),
      headers: {'Content-Type': 'application/json'},
      body: jsonEncode({'email': email, 'password': password}),
    );
    
    if (response.statusCode == 200) {
      final data = jsonDecode(response.body);
      await _storeToken(data['token']);
      return LoginResponse.fromJson(data);
    } else {
      throw ApiException.fromResponse(response);
    }
  }
}
```

### 3. Synchronisation hors ligne
```dart
class SyncService {
  Future<void> syncData() async {
    try {
      // 1. Récupérer les données initiales
      final initialData = await _getInitialData();
      await _storeLocalData(initialData);
      
      // 2. Récupérer les données utilisateur
      final userData = await _getUserData();
      await _storeUserData(userData);
      
      // 3. Marquer comme synchronisé
      await _markAsSynced();
    } catch (e) {
      // Gérer les erreurs de synchronisation
      print('Erreur de synchronisation: $e');
    }
  }
}
```

## 🧪 Tests avec curl/PowerShell

### Test de connexion
```bash
curl -X POST http://localhost:8000/api/mobile/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"doctor123"}'
```

### Test avec PowerShell
```powershell
Invoke-RestMethod -Uri "http://localhost:8000/api/mobile/auth/login" `
  -Method Post -ContentType "application/json" `
  -Body '{"email":"<EMAIL>","password":"doctor123"}'
```

## 🔄 Workflow de développement mobile

1. **Connexion** → Récupérer le token JWT
2. **Données initiales** → Synchroniser les données de référence
3. **Données utilisateur** → Récupérer les données spécifiques au rôle
4. **Stockage local** → Sauvegarder en SQLite pour le mode hors ligne
5. **Synchronisation périodique** → Rafraîchir les données quand connecté

Cette API est optimisée pour le fonctionnement hors ligne de votre application Flutter PTCCare ! 🚀
